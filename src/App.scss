.mic-button:disabled {
  opacity: 0.9 !important;
}

.modal-fullscreen-wrapper {
  position: absolute !important;
}

// Character Generation Loader Styles
.character-generation-loader {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 9999 !important;
  background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 40, 80, 0.95)) !important;
  backdrop-filter: blur(10px) !important;

  // Ensure text is visible and styled
  color: #ffffff !important;

  // Center content
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: column !important;

  // Text styling
  font-size: 1.1rem !important;
  text-align: center !important;

  // Add some spacing between text lines
  > * {
    margin: 8px 0 !important;
    line-height: 1.4 !important;
  }
}

.primary-button {
  box-shadow: 0px 0px 12px 0px #88ffd5;
  background-color: #88FFD5;
  border-radius: 0.5rem;
  color: black;
}

// Reset section styles
.reset-section {
  display: flex;
  justify-content: center;
  margin-top: 16px;

  .reset-button {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #c82333;
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }
}
