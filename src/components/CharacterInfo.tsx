import React, { useState, useEffect } from "react";
import ReactMarkdown from "react-markdown";
import { PerplexityService } from "../services/PerplexityService";
import type { PerplexityGenerateResponse } from "../types/PerplexityTypes";
import { Modal } from "microapps";

interface CharacterInfoProps {
  characterName: string;
  isVisible: boolean;
  onClose: () => void;
}

export const CharacterInfo: React.FC<CharacterInfoProps> = ({
  characterName,
  isVisible,
  onClose,
}) => {
  const [characterInfo, setCharacterInfo] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");

  const perplexityService = PerplexityService.getInstance();

  /**
   * Fetch character information when component becomes visible
   */
  useEffect(() => {
    if (isVisible && characterName && !characterInfo) {
      fetchCharacterInfo();
    }
  }, [isVisible, characterName]);

  /**
   * Fetch character information from Perplexity
   */
  const fetchCharacterInfo = async () => {
    if (!characterName.trim()) return;

    setLoading(true);
    setError("");

    try {
      const response: PerplexityGenerateResponse =
        await perplexityService.getCharacterInfo(characterName);

      if (response.ok && response.output) {
        setCharacterInfo(response.output);
      } else {
        throw new Error(
          response.message || "No se pudo obtener información del personaje"
        );
      }
    } catch (err) {
      console.error("❌ Error fetching character info:", err);
      setError(
        err instanceof Error
          ? err.message
          : "Error desconocido al obtener información"
      );
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle retry action
   */
  const handleRetry = () => {
    setCharacterInfo("");
    setError("");
    fetchCharacterInfo();
  };

  /**
   * Handle close action
   */
  const handleClose = () => {
    setCharacterInfo("");
    setError("");
    onClose();
  };

  // Detectar si el contenido tiene formato Markdown
  const hasMarkdown =
    characterInfo &&
    (characterInfo.includes("#") ||
      characterInfo.includes("**") ||
      characterInfo.includes("*") ||
      characterInfo.includes("\n-") ||
      characterInfo.includes("\n1.") ||
      characterInfo.includes("###"));

  const 

  if (!isVisible) return null;

  return (
    <>
      <Modal
        onClose={handleClose}
        title="Información del Personaje"
        body="Subtitle"
      />

      {/* <div
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          zIndex: 1000,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "20px",
        }}
        onClick={handleClose}
      >
        <div
          style={{
            backgroundColor: "white",
            borderRadius: "12px",
            padding: "24px",
            maxWidth: "600px",
            width: "100%",
            maxHeight: "80vh",
            overflowY: "auto",
            boxShadow: "0 10px 25px rgba(0, 0, 0, 0.2)",
            position: "relative",
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "20px",
              borderBottom: "1px solid #e9ecef",
              paddingBottom: "16px",
            }}
          >
            <h2
              style={{
                margin: 0,
                color: "#333",
                fontSize: "1.5rem",
                fontWeight: "600",
              }}
            >
              📖 Información del Personaje
            </h2>
          </div>

          <div
            style={{
              backgroundColor: "#f8f9fa",
              padding: "12px 16px",
              borderRadius: "8px",
              marginBottom: "20px",
              border: "1px solid #e9ecef",
            }}
          >
            <h3
              style={{
                margin: 0,
                color: "#495057",
                fontSize: "1.2rem",
                fontWeight: "500",
              }}
            >
              🎭 {characterName}
            </h3>
          </div>

          <div style={{ minHeight: "200px" }}>
            {loading && (
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  padding: "40px",
                  color: "#666",
                }}
              >
                <div
                  style={{
                    width: "40px",
                    height: "40px",
                    border: "4px solid #f3f3f3",
                    borderTop: "4px solid #007bff",
                    borderRadius: "50%",
                    animation: "spin 1s linear infinite",
                    marginBottom: "16px",
                  }}
                />
                <p>Buscando información sobre {characterName}...</p>
              </div>
            )}

            {error && (
              <div
                style={{
                  backgroundColor: "#f8d7da",
                  color: "#721c24",
                  padding: "16px",
                  borderRadius: "8px",
                  border: "1px solid #f5c6cb",
                  textAlign: "center",
                }}
              >
                <p style={{ margin: "0 0 12px 0" }}>❌ {error}</p>
                <button
                  onClick={handleRetry}
                  style={{
                    backgroundColor: "#dc3545",
                    color: "white",
                    border: "none",
                    padding: "8px 16px",
                    borderRadius: "4px",
                    cursor: "pointer",
                    fontSize: "14px",
                  }}
                >
                  🔄 Reintentar
                </button>
              </div>
            )}

            {characterInfo && !loading && !error && (
              <div
                style={{
                  backgroundColor: "#f8f9fa",
                  padding: "20px",
                  borderRadius: "8px",
                  border: "1px solid #e9ecef",
                  lineHeight: "1.6",
                  color: "#495057",
                }}
              >
                {hasMarkdown ? (
                  <ReactMarkdown
                    components={{
                      h1: ({ children }) => (
                        <h1
                          style={{
                            color: "#007bff",
                            fontSize: "1.8rem",
                            fontWeight: "600",
                            marginBottom: "16px",
                            marginTop: "20px",
                            borderBottom: "2px solid #e9ecef",
                            paddingBottom: "8px",
                          }}
                        >
                          {children}
                        </h1>
                      ),
                      h2: ({ children }) => (
                        <h2
                          style={{
                            color: "#007bff",
                            fontSize: "1.5rem",
                            fontWeight: "600",
                            marginBottom: "14px",
                            marginTop: "20px",
                          }}
                        >
                          {children}
                        </h2>
                      ),
                      h3: ({ children }) => (
                        <h3
                          style={{
                            color: "#495057",
                            fontSize: "1.3rem",
                            fontWeight: "600",
                            marginBottom: "12px",
                            marginTop: "16px",
                          }}
                        >
                          {children}
                        </h3>
                      ),
                      p: ({ children }) => (
                        <p
                          style={{
                            marginBottom: "12px",
                            lineHeight: "1.7",
                            color: "#495057",
                          }}
                        >
                          {children}
                        </p>
                      ),
                      ul: ({ children }) => (
                        <ul
                          style={{
                            paddingLeft: "20px",
                            marginBottom: "12px",
                          }}
                        >
                          {children}
                        </ul>
                      ),
                      ol: ({ children }) => (
                        <ol
                          style={{
                            paddingLeft: "20px",
                            marginBottom: "12px",
                          }}
                        >
                          {children}
                        </ol>
                      ),
                      li: ({ children }) => (
                        <li
                          style={{
                            marginBottom: "6px",
                            lineHeight: "1.6",
                          }}
                        >
                          {children}
                        </li>
                      ),
                      strong: ({ children }) => (
                        <strong
                          style={{
                            color: "#007bff",
                            fontWeight: "600",
                          }}
                        >
                          {children}
                        </strong>
                      ),
                      em: ({ children }) => (
                        <em
                          style={{
                            fontStyle: "italic",
                            color: "#6c757d",
                          }}
                        >
                          {children}
                        </em>
                      ),
                      blockquote: ({ children }) => (
                        <blockquote
                          style={{
                            borderLeft: "4px solid #007bff",
                            paddingLeft: "16px",
                            marginLeft: "0",
                            marginBottom: "16px",
                            fontStyle: "italic",
                            color: "#6c757d",
                            backgroundColor: "#f8f9fa",
                          }}
                        >
                          {children}
                        </blockquote>
                      ),
                      code: ({ children, className }) => {
                        const isInline = !className;
                        return isInline ? (
                          <code
                            style={{
                              backgroundColor: "#e9ecef",
                              padding: "2px 6px",
                              borderRadius: "4px",
                              fontSize: "0.9em",
                              color: "#e83e8c",
                            }}
                          >
                            {children}
                          </code>
                        ) : (
                          <code
                            style={{
                              display: "block",
                              backgroundColor: "#f8f9fa",
                              padding: "12px",
                              borderRadius: "8px",
                              fontSize: "0.9em",
                              border: "1px solid #e9ecef",
                              overflowX: "auto",
                            }}
                          >
                            {children}
                          </code>
                        );
                      },
                    }}
                  >
                    {characterInfo}
                  </ReactMarkdown>
                ) : (
                  <div
                    style={{
                      whiteSpace: "pre-wrap",
                      fontSize: "15px",
                    }}
                  >
                    {characterInfo}
                  </div>
                )}
              </div>
            )}
          </div>

          <div
            style={{
              marginTop: "24px",
              paddingTop: "16px",
              borderTop: "1px solid #e9ecef",
              textAlign: "center",
            }}
          >
            <button
              onClick={handleClose}
              style={{
                backgroundColor: "#6c757d",
                color: "white",
                border: "none",
                padding: "10px 20px",
                borderRadius: "6px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "500",
              }}
              onMouseOver={(e) =>
                (e.currentTarget.style.backgroundColor = "#5a6268")
              }
              onMouseOut={(e) =>
                (e.currentTarget.style.backgroundColor = "#6c757d")
              }
            >
              Cerrar
            </button>
          </div>
        </div>
      </div> */}
    </>
  );
};
