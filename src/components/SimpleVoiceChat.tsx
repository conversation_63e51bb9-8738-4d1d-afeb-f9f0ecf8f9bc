// React core
import React, { useEffect, useRef, useState } from "react";
// Third-party library imports
// Services
import type { GameProgress as GameProgressType } from "../services/ConversationStorage";
// Components
import { MessageBubble } from "./MessageBubble";
import { ConversationStorage } from "../services/ConversationStorage";
// Utils & Constants & Helpers
import { useRealTimeConversation } from "../hooks/useRealTimeConversation";
import type { ConversationMessage } from "../utils/conversationUtils";
// Styles

const UnsupportedBrowserMessage = () => (
  <div className="unsupported-browser-warning">
    <h4>⚠️ Reconocimiento de voz no disponible</h4>
    <p>Tu navegador no soporta esta funcionalidad</p>
  </div>
);

interface SimpleVoiceChatProps {
  generatedCharacter?: string;
  isGameStarted: boolean;
  initialMessage?: string;
  questionsRemaining?: number;
  onGameEnd?: (gameWon: boolean) => void;
}

interface CountdownMessages {
  questionsCountdownMessages: Record<string, string>;
}

export const SimpleVoiceChat: React.FC<SimpleVoiceChatProps> = ({
  generatedCharacter,
  isGameStarted,
  initialMessage,
  questionsRemaining,
  onGameEnd,
}) => {
  // State for game management
  const [gameProgress, setGameProgress] = useState<GameProgressType | null>(
    null
  );
  const [countdownMessages, setCountdownMessages] =
    useState<CountdownMessages | null>(null);
  const gameEndTriggered = useRef(false);

  // Refs
  const conversationStorage = useRef(ConversationStorage.getInstance());

  // Hook for real-time conversation management
  const {
    isActive,
    messages,
    isSupported,
    error,
    stopConversation,
    addInitialMessage,
  } = useRealTimeConversation(
    generatedCharacter,
    isGameStarted,
    gameProgress?.gameFinished
  );

  /**
   * Get appropriate empty state message based on current state
   */
  const getEmptyStateMessage = () => {
    if (isActive) {
      return "¡Conversación activa! Puedes empezar a hablar";
    }
    if (isGameStarted && initialMessage) {
      return "Activando chat de voz automáticamente...";
    }
    return "Inicia la conversación para comenzar a chatear";
  };

  useEffect(() => {
    const loadCountdownMessages = async () => {
      try {
        const response = await fetch("/questions-countdown-messages.json");
        const data: CountdownMessages = await response.json();
        setCountdownMessages(data);
      } catch (error) {
        console.error("Error loading countdown messages:", error);
      }
    };

    loadCountdownMessages();
  }, []);

  /**
   * Add initial message when it arrives
   */
  useEffect(() => {
    if (initialMessage && isGameStarted) {
      addInitialMessage(initialMessage);
    }
  }, [initialMessage, isGameStarted, addInitialMessage]);

  /**
   * Update game progress when session changes
   */
  useEffect(() => {
    if (!isGameStarted) return;

    const updateProgress = () => {
      const progress = conversationStorage.current.getGameProgress();
      setGameProgress(progress);

      // Notify parent when game finishes (only once)
      if (progress?.gameFinished && !gameEndTriggered.current && onGameEnd) {
        // console.log("🎮 Game finished detected:", {
        //   gameFinished: progress.gameFinished,
        //   gameWon: progress.gameWon,
        // });
        gameEndTriggered.current = true;
        onGameEnd(progress.gameWon);
      }
    };

    updateProgress();
    const interval = setInterval(updateProgress, 1000);
    return () => clearInterval(interval);
  }, [isGameStarted, messages, onGameEnd]);

  /**
   * Reset flags when game restarts
   */
  useEffect(() => {
    if (!isGameStarted) {
      gameEndTriggered.current = false;
    }
  }, [isGameStarted]);

  // Helper to check if game has finished
  const isGameFinished = gameProgress?.gameFinished ?? false;

  /**
   * Stop conversation when game finishes
   */
  useEffect(() => {
    if (isGameFinished && isActive) {
      // console.log("🔇 Game finished, stopping conversation");
      stopConversation();
    }
  }, [isGameFinished, isActive, stopConversation]);

  // /**
  //  * Debug: Log messages when they change
  //  */
  // useEffect(() => {
  //   // console.log("🔍 [SimpleVoiceChat] Messages updated:", {
  //   //   count: messages.length,
  //   //   messages: messages.map(m => ({ id: m.id, type: m.type, content: m.content.substring(0, 50) + '...' }))
  //   // });
  // }, [messages]);

  // Función para obtener el mensaje de cuenta regresiva
  const getCountdownMessage = (remainingQuestions: number): string | null => {
    if (!countdownMessages) return null;

    const messageKey = remainingQuestions.toString();
    return countdownMessages.questionsCountdownMessages[messageKey] || null;
  };

  if (!isGameStarted) {
    return null;
  }

  if (!isSupported) {
    return <UnsupportedBrowserMessage />;
  }

  return (
    <div
      className="voice-chat-container"
      style={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        overflow: "hidden",
      }}
    >
      <div className="character-title">
        <span>{generatedCharacter}</span>
      </div>

      {/* CSS for animations and scrollbar hiding */}
      <style>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        /* Hide scrollbar for webkit browsers (Chrome, Safari, Edge) */
        .hidden-scrollbar::-webkit-scrollbar {
          display: none;
        }

        /* Hide scrollbar for Firefox */
        .hidden-scrollbar {
          scrollbar-width: none;
        }

        /* For IE and older Edge */
        .hidden-scrollbar {
          -ms-overflow-style: none;
        }
      `}</style>

      {/* Chat area */}
      <div
        style={{
          // border: "1px solid #88FFD5",
          // borderRadius: "12px",
          width: "100%",
          maxWidth: "600px",
          height: "500px",
          // backgroundColor: "#002332",
          position: "relative",
          overflow: "hidden", // Hide overflow to prevent seeing partial messages
        }}
      >
        {messages.length === 0 ? (
          <EmptyState message={getEmptyStateMessage()} />
        ) : (
          <MessageList messages={messages} />
        )}
      </div>

      {(() => {
        const countdownMessage = gameProgress
          ? getCountdownMessage(gameProgress.questionsRemaining)
          : null;
        return countdownMessage ? (
          <div className="countdown-message" style={{
            color: "#88FFD5",
            fontFamily: "On Air",
            fontSize: "22px",
            fontWeight: "400",
            fontStyle: "italic",
            lineHeight: "36px",
          }}>{countdownMessage}</div>
        ) : null;
      })()}
    </div>
  );
};

/**
 * Empty state component when no messages are present
 */
interface EmptyStateProps {
  message: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({ message }) => (
  <div
    style={{
      textAlign: "center",
      color: "#6c757d",
      fontSize: "16px",
      paddingTop: "60px",
    }}
  >
    <div style={{ fontSize: "48px", marginBottom: "16px" }}>💬</div>
    <p style={{ margin: "8px 0" }}>{message}</p>
    <p
      style={{
        fontSize: "13px",
        opacity: 0.8,
        marginTop: "12px",
      }}
    >
      El micrófono se gestiona automáticamente
    </p>
  </div>
);

/**
 * Message list component that shows one message at a time
 * Only the latest message is visible, previous ones are accessible via scroll
 */
interface MessageListProps {
  messages: ConversationMessage[];
}

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to show the latest message when new messages arrive
  useEffect(() => {
    if (containerRef.current && messages.length > 0) {
      // Scroll to the last message (each message is exactly 500px tall)
      const lastMessageIndex = messages.length - 1;
      containerRef.current.scrollTop = lastMessageIndex * 500;
    }
  }, [messages.length]);

  // console.log("🔍 [MessageList] Renderizando con", messages.length, "mensajes");

  if (messages.length === 0) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className="hidden-scrollbar"
      style={{
        height: "100%",
        overflowY: "auto",
        scrollBehavior: "smooth",
        scrollSnapType: "y mandatory", // Snap to each message
      }}
    >
      {/* Render all messages, each taking exactly the full container height */}
      {messages.map((message, index) => {
        // console.log("🔍 [MessageList] Renderizando mensaje", index, message.id, message.type);

        return (
          <div
            key={message.id}
            style={{
              height: "500px", // Exact height to match container
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              padding: "20px",
              boxSizing: "border-box",
              scrollSnapAlign: "start", // Snap alignment
            }}
          >
            <MessageBubble message={message} />
          </div>
        );
      })}
    </div>
  );
};
