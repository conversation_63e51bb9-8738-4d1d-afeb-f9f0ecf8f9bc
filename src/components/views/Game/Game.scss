.view-game {
  height: calc(100vh - 150px);
  display: flex;

  .container {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    width: 100%;
  }

  .menu-left {
    display: flex;
    align-items: flex-end;

    .enygma-logo {
      position: relative;
      left: 0;
      bottom: 0;
      padding: 2rem;

      .enygma-image {
        border-radius: 138px;
        border: 2px solid #88ffd5;
        box-shadow: 0px 0px 12px 0px #88ffd5;
      }

      .speaking {
        animation: aura-pulse 2s infinite;
        border-radius: 61px;
        box-shadow: 0 0 20px rgba(136, 255, 213, 0.6);
        position: absolute;
        top: 0rem;
        right: 0rem;
      }

      .icon-aura {
        width: 90px;
        height: 90px;
        border-radius: 61px;
        padding: 16px;
        background: #88ffd5;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &.speech-indicator {
          .speech-pulse {
            background: #40e0d0;
            color: #001428;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            animation: speech-pulse 1.5s infinite;
            box-shadow: 0 2px 8px rgba(64, 224, 208, 0.4);
          }
        }
      }
    }
  }

  .menu-right {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    height: 100%;
    padding: 1rem;

    .game-navigation {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      height: 100%;
      justify-content: space-around;

      .image-button {
        padding: 1rem;
        width: 150px;
        position: relative;
        left: 0;
        bottom: 0;
        z-index: 2;
        align-items: center;
        display: flex;
        flex-direction: column;
        transition: all 0.2s;
        cursor: pointer;
      }
    }
  }
}
