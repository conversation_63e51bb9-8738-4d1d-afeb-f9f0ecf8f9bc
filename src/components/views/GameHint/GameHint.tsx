import React, { useEffect, useState } from "react";
import "./GameHint.scss";
import { ConversationStorage, type GameHint as GameHintType } from "../../../services/ConversationStorage";
import { Bubble } from "microapps";

/**
 * Game Hint Component
 *
 * Displays all the hints/clues discovered during the game.
 * Features:
 * - List of all discovered hints
 * - Chronological order
 * - Visual representation of progress
 */
export const GameHint: React.FC = () => {
  const [hints, setHints] = useState<GameHintType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHintsFromStorage();

    // Set up interval to check for new hints periodically
    const interval = setInterval(loadHintsFromStorage, 1000);

    return () => clearInterval(interval);
  }, []);

  const loadHintsFromStorage = () => {
    try {
      const storage = ConversationStorage.getInstance();
      const currentSession = storage.getCurrentSession();

      if (currentSession?.gameProgress?.hints) {
        setHints(currentSession.gameProgress.hints);
      } else {
        setHints([]);
      }
    } catch (error) {
      console.error("Error al cargar las pistas:", error);
      setHints([]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="game-hint">
        <div className="game-hint-content">
          <div className="hints-header">
            <div className="hints-icon">💡</div>
            <h1 className="hints-title">Cargando pistas...</h1>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="game-hint">
      <div className="game-hint-content">
        {hints.length === 0 ? (
          <div className="no-hints">
            <p className="no-clues-message body1">
              Aquí aparecerán las pistas que vayas descubriendo... Por ahora, los secretos
              permanecen ocultos en las sombras. Hazme tu primera pregunta y comenzaré a revelarlos.
            </p>
          </div>
        ) : (
          <div className="hints-list">
            {hints.map((hint) => (
              <div key={hint.id} className="hint-clue">
                <Bubble
                  text={hint.content}
                  backgroundColor="#D6FDF1"
                  textColor="#000"
                  className="clue-tag"
                  id={hint.id}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
