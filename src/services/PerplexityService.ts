import axios from "axios";
import type {
  PerplexityGenerateResponse,
  PerplexityResetResponse,
  PerplexitySelectedPresetResponse,
  PerplexityPresetResponse,
  PerplexityGenerateInput,
} from "../types/PerplexityTypes";
import type { IPerplexityService } from "./impl/IPerplexityService";

const BASE_URL = import.meta.env.VITE_PERPLEXITY_BASE_URL;
const API_KEY = import.meta.env.VITE_PERPLEXITY_API_KEY;

export class PerplexityService implements IPerplexityService {
  private static instance: PerplexityService;
  private sesid = "";
  private presetId = "";

  constructor() {
    // Validate environment variables
    if (!BASE_URL) {
      console.error("❌ VITE_PERPLEXITY_BASE_URL no está configurada");
    }
    if (!API_KEY) {
      console.error("❌ VITE_PERPLEXITY_API_KEY no está configurada");
    }
  }

  /**
   * Singleton pattern implementation
   */
  static getInstance(): PerplexityService {
    if (!PerplexityService.instance) {
      PerplexityService.instance = new PerplexityService();
    }
    return PerplexityService.instance;
  }

  setSesid(id: string): void {
    this.sesid = id;
  }

  setPresetId(id: string): void {
    // console.log("Perplexity preset id: ", id);
    this.presetId = id;
  }

  private getHeaders(): Record<string, string> {
    return {
      "Access-Control-Allow-Origin": "*",
      "Content-Type": "application/json",
      "X-Api-Key": API_KEY || "",
      "X-MG-Ses": this.sesid,
      "X-Frame-Options": "SAMEORIGIN",
    };
  }

  private async handleRequest<T>(request: Promise<any>): Promise<T> {
    try {
      const res = await request;
      if (import.meta.env.MODE === "development") {
        // console.log(`🟢 Perplexity ${res.status} ${res.config.url}`, res.data);
      }
      return res.data as T;
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        if (error instanceof Error) {
          console.error(`🔴 PERPLEXITY ERROR: ${error.message}`);
        } else {
          console.error("🔴 PERPLEXITY ERROR: Unknown error", error);
        }
      }
      throw new Error("Error al realizar la solicitud a Perplexity");
    }
  }

  async generate(
    text: string,
    id?: string
  ): Promise<PerplexityGenerateResponse> {
    if (!BASE_URL) {
      throw new Error(
        "VITE_PERPLEXITY_BASE_URL no está configurada. Verifica tu archivo .env"
      );
    }

    let data: PerplexityGenerateInput = {
      sesid: this.sesid,
      preset: this.presetId,
      query: text,
      query_args: {},
    };
    if (id) {
      data.query_args.query_id = id;
    }

    return this.handleRequest<PerplexityGenerateResponse>(
      axios.post(`${BASE_URL}generate`, data, { headers: this.getHeaders() })
    );
  }

  async reset(): Promise<PerplexityResetResponse> {
    if (!BASE_URL) {
      throw new Error(
        "VITE_PERPLEXITY_BASE_URL no está configurada. Verifica tu archivo .env"
      );
    }

    if (this.sesid !== "") {
      return this.handleRequest<PerplexityResetResponse>(
        axios.get(`${BASE_URL}reset/${this.sesid}?preset=${this.presetId}`, {
          headers: this.getHeaders(),
        })
      );
    } else {
      return Promise.reject("reset");
    }
  }

  async selectPreset(): Promise<PerplexitySelectedPresetResponse> {
    if (!BASE_URL) {
      throw new Error(
        "VITE_PERPLEXITY_BASE_URL no está configurada. Verifica tu archivo .env"
      );
    }

    return this.handleRequest<PerplexitySelectedPresetResponse>(
      axios.get(`${BASE_URL}preset/${this.presetId}`, {
        headers: this.getHeaders(),
      })
    );
  }

  async getPresets(): Promise<PerplexityPresetResponse> {
    if (!BASE_URL) {
      throw new Error(
        "VITE_PERPLEXITY_BASE_URL no está configurada. Verifica tu archivo .env"
      );
    }

    // console.log("Perplexity getPresets: ", `${BASE_URL}presets`);
    return this.handleRequest<PerplexityPresetResponse>(
      axios.get(`${BASE_URL}presets`, { headers: this.getHeaders() })
    );
  }

  /**
   * Get character information from Perplexity
   * @param characterName - Name of the character to get information about
   * @returns Promise with character information
   */
  async getCharacterInfo(
    characterName: string
  ): Promise<PerplexityGenerateResponse> {
    const query = `Proporciona información detallada sobre ${characterName}. Incluye: biografía, características principales, logros importantes, curiosidades y datos relevantes. Mantén la información concisa pero completa.`;

    // console.log(`🔍 Buscando información sobre: ${characterName}`);

    return this.generate(query);
  }
}
